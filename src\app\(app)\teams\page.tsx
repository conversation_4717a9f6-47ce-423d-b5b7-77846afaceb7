import React from 'react'
import getTeams from '@/actions/teams/get-all-teams';
import HtmlContentDisplay from '@/components/html-content-display';
import Image from 'next/image';

export default async function OurTeamPage() {
    const teamsResult = await getTeams();
    const team = teamsResult?.data || [];

    return (
        <>
            <section className="relative md:h-screen h-[250px] min-h-[300px] overflow-hidden flex items-center justify-center">
                <Image
                    src={team?.image || "/images/fastpacking/hero/image2.webp"}
                    alt="Mountains in the mist"
                    fill
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat object-cover"
                />
            </section>

            <div className="container mx-auto px-4 py-8">
                <h2 className="text-3xl md:text-4xl font-bold mb-8 text-center text-brand">{team.title}</h2>
                {team.members?.map(member => (
                    <div key={member.id}
                        className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12 items-start">
                        <div className="md:col-span-1">
                            <Image
                                src={member.image}
                                alt={member.name}
                                width={400}
                                height={400}
                                className="rounded-lg shadow w-full h-auto object-cover mb-4"
                            />
                            <div className="text-lg font-semibold">{member.name}</div>
                            <div className="text-sm text-gray-600">{member.role}</div>
                        </div>
                        <div className="md:col-span-3 flex flex-col justify-center">
                            <div className="text-base text-gray-800 whitespace-pre-line">
                                <HtmlContentDisplay
                                    htmlContent={member.bio}
                                    className="text-justify"
                                />
                            </div>
                        </div>
                    </div>

                ))}
            </div>
        </>
    )
}
